/**
 * Firebase Cloud Messaging interfaces and types
 */

export interface FCMNotificationPayload {
  title?: string;
  body?: string;
  icon?: string;
  image?: string;
  badge?: string;
  sound?: string;
  tag?: string;
  color?: string;
  click_action?: string;
  body_loc_key?: string;
  body_loc_args?: string[];
  title_loc_key?: string;
  title_loc_args?: string[];
}

export interface FCMDataPayload {
  [key: string]: string;
}

export interface FCMMessage {
  notification?: FCMNotificationPayload;
  data?: FCMDataPayload;
  from?: string;
  messageId?: string;
  collapseKey?: string;
}

export interface FCMTokenInfo {
  token: string;
  timestamp: number;
  isValid: boolean;
}

export interface NotificationPermissionStatus {
  permission: NotificationPermission;
  isSupported: boolean;
  canRequest: boolean;
}

export interface FCMServiceStatus {
  isInitialized: boolean;
  hasPermission: boolean;
  token: string | null;
  error: string | null;
  lastUpdated: Date;
}

export interface FCMError {
  code: string;
  message: string;
  details?: any;
}

export enum FCMEventType {
  TOKEN_RECEIVED = 'token_received',
  TOKEN_REFRESHED = 'token_refreshed',
  MESSAGE_RECEIVED = 'message_received',
  PERMISSION_GRANTED = 'permission_granted',
  PERMISSION_DENIED = 'permission_denied',
  ERROR_OCCURRED = 'error_occurred'
}

export interface FCMEvent {
  type: FCMEventType;
  data?: any;
  timestamp: Date;
}
