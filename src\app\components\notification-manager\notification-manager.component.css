.notification-manager {
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 2rem;
}

.header h2 {
  color: #1a73e8;
  margin-bottom: 0.5rem;
  font-size: 2rem;
}

.header p {
  color: #5f6368;
  font-size: 1.1rem;
}

/* Loading */
.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
  margin-bottom: 1rem;
}

.spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e3e3e3;
  border-top: 2px solid #1a73e8;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Error <PERSON> */
.error-banner {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: #fef7f0;
  border: 1px solid #f9ab00;
  border-radius: 8px;
  margin-bottom: 1rem;
  color: #b06000;
}

.error-icon {
  font-size: 1.2rem;
}

.close-btn {
  margin-left: auto;
  background: none;
  border: none;
  font-size: 1.5rem;
  cursor: pointer;
  color: #b06000;
}

/* Status Grid */
.status-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1rem;
  margin-bottom: 2rem;
}

.status-card {
  background: white;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.status-card h3 {
  margin: 0 0 1rem 0;
  color: #202124;
  font-size: 1.1rem;
}

.status-indicator {
  margin-bottom: 1rem;
}

.status-indicator span {
  padding: 0.5rem 1rem;
  border-radius: 20px;
  font-weight: 500;
  font-size: 0.9rem;
}

.status-granted {
  background: #e8f5e8;
  color: #137333;
}

.status-denied {
  background: #fce8e6;
  color: #d93025;
}

.status-default {
  background: #fef7f0;
  color: #b06000;
}

.status-unknown, .status-loading {
  background: #f1f3f4;
  color: #5f6368;
}

.status-success {
  background: #e8f5e8;
  color: #137333;
}

.status-error {
  background: #fce8e6;
  color: #d93025;
}

.status-warning {
  background: #fef7f0;
  color: #b06000;
}

.status-detail {
  margin: 0.5rem 0;
  font-size: 0.9rem;
  color: #5f6368;
}

.status-detail.error {
  color: #d93025;
}

/* Sections */
.section {
  background: white;
  border: 1px solid #e8eaed;
  border-radius: 12px;
  padding: 1.5rem;
  margin-bottom: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.section-header h3 {
  margin: 0;
  color: #202124;
  font-size: 1.2rem;
}

.section-actions {
  display: flex;
  gap: 0.5rem;
}

/* Buttons */
.btn {
  padding: 0.75rem 1.5rem;
  border: none;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #1a73e8;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #1557b0;
}

.btn-secondary {
  background: #f8f9fa;
  color: #3c4043;
  border: 1px solid #dadce0;
}

.btn-secondary:hover:not(:disabled) {
  background: #f1f3f4;
}

.btn-danger {
  background: #d93025;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #b52d20;
}

.btn-outline {
  background: transparent;
  color: #1a73e8;
  border: 1px solid #1a73e8;
}

.btn-outline:hover:not(:disabled) {
  background: #f8f9ff;
}

/* Token Management */
.token-actions {
  display: flex;
  gap: 1rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.token-details {
  margin-top: 1rem;
  padding: 1rem;
  background: #f8f9fa;
  border-radius: 8px;
}

.token-details h4 {
  margin: 0 0 1rem 0;
  color: #202124;
}

.token-display {
  background: white;
  border: 1px solid #dadce0;
  border-radius: 6px;
  padding: 1rem;
  margin-bottom: 1rem;
  word-break: break-all;
  font-family: 'Courier New', monospace;
  font-size: 0.85rem;
  max-height: 150px;
  overflow-y: auto;
}

.token-info {
  margin: 0;
  font-size: 0.9rem;
  color: #5f6368;
}

/* Message and Event Lists */
.message-list, .event-list {
  max-height: 400px;
  overflow-y: auto;
}

.message-item, .event-item {
  padding: 1rem;
  border: 1px solid #e8eaed;
  border-radius: 8px;
  margin-bottom: 1rem;
  background: #fafafa;
}

.message-header, .event-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.5rem;
}

.message-id {
  font-size: 0.8rem;
  color: #5f6368;
  font-family: monospace;
}

.message-body {
  margin: 0.5rem 0;
  color: #3c4043;
}

.message-data, .event-data {
  margin-top: 1rem;
  padding: 0.75rem;
  background: white;
  border-radius: 4px;
  border: 1px solid #e8eaed;
}

.message-data pre, .event-data pre {
  margin: 0.5rem 0 0 0;
  font-size: 0.8rem;
  color: #5f6368;
  white-space: pre-wrap;
  word-break: break-word;
}

.event-type {
  font-weight: 500;
  color: #1a73e8;
}

.event-time {
  font-size: 0.8rem;
  color: #5f6368;
  font-family: monospace;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 2rem;
  color: #5f6368;
}

.empty-state p {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
}

.empty-state small {
  font-size: 0.9rem;
}

/* Instructions */
.instructions {
  background: #f8f9ff;
  border-color: #e8eaff;
}

.instructions h3 {
  color: #1a73e8;
  margin-bottom: 1rem;
}

.instructions ol {
  margin: 0;
  padding-left: 1.5rem;
}

.instructions li {
  margin-bottom: 0.75rem;
  line-height: 1.5;
}

.instructions code {
  background: #f1f3f4;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-family: 'Courier New', monospace;
  font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
  .notification-manager {
    padding: 1rem;
  }
  
  .status-grid {
    grid-template-columns: 1fr;
  }
  
  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .token-actions {
    flex-direction: column;
  }
  
  .btn {
    width: 100%;
    justify-content: center;
  }
}
