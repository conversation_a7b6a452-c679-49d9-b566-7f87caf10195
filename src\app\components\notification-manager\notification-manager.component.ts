import { Component, OnInit, OnDestroy, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject, takeUntil } from 'rxjs';
import { FCMService } from '../../services/fcm.service';
import { TestNotificationComponent } from '../test-notification/test-notification.component';
import { 
  FCMServiceStatus, 
  NotificationPermissionStatus, 
  FCMMessage, 
  FCMEvent, 
  FCMEventType 
} from '../../interfaces/fcm.interface';

@Component({
  selector: 'app-notification-manager',
  standalone: true,
  imports: [CommonModule, TestNotificationComponent],
  templateUrl: './notification-manager.component.html',
  styleUrl: './notification-manager.component.css'
})
export class NotificationManagerComponent implements OnInit, OnDestroy {
  private readonly fcmService = inject(FCMService);
  private readonly destroy$ = new Subject<void>();

  // Component state
  fcmStatus: FCMServiceStatus | null = null;
  permissionStatus: NotificationPermissionStatus | null = null;
  currentToken: string | null = null;
  recentMessages: FCMMessage[] = [];
  recentEvents: FCMEvent[] = [];
  isLoading = false;
  error: string | null = null;

  // UI state
  showTokenDetails = false;
  showEventLog = false;
  showMessageHistory = false;

  ngOnInit(): void {
    this.initializeComponent();
    this.subscribeToFCMUpdates();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  /**
   * Initialize component state
   */
  private initializeComponent(): void {
    this.permissionStatus = this.fcmService.getPermissionStatus();
    
    // Load stored token if available
    const storedToken = this.fcmService.getStoredToken();
    if (storedToken?.isValid) {
      this.currentToken = storedToken.token;
    }
  }

  /**
   * Subscribe to FCM service updates
   */
  private subscribeToFCMUpdates(): void {
    // Subscribe to status updates
    this.fcmService.status$
      .pipe(takeUntil(this.destroy$))
      .subscribe(status => {
        this.fcmStatus = status;
        this.currentToken = status.token;
        this.error = status.error;
      });

    // Subscribe to token updates
    this.fcmService.token$
      .pipe(takeUntil(this.destroy$))
      .subscribe(token => {
        this.currentToken = token;
      });

    // Subscribe to incoming messages
    this.fcmService.messages$
      .pipe(takeUntil(this.destroy$))
      .subscribe(message => {
        this.recentMessages.unshift(message);
        // Keep only last 10 messages
        if (this.recentMessages.length > 10) {
          this.recentMessages = this.recentMessages.slice(0, 10);
        }
      });

    // Subscribe to FCM events
    this.fcmService.events$
      .pipe(takeUntil(this.destroy$))
      .subscribe(event => {
        this.recentEvents.unshift(event);
        // Keep only last 20 events
        if (this.recentEvents.length > 20) {
          this.recentEvents = this.recentEvents.slice(0, 20);
        }
      });
  }

  /**
   * Request notification permission
   */
  async requestPermission(): Promise<void> {
    this.isLoading = true;
    this.error = null;

    try {
      this.permissionStatus = await this.fcmService.requestPermission();
    } catch (error) {
      this.error = 'Failed to request notification permission';
      console.error('Permission request failed:', error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Generate FCM token
   */
  async generateToken(): Promise<void> {
    this.isLoading = true;
    this.error = null;

    try {
      const token = await this.fcmService.getToken();
      if (!token) {
        this.error = 'Failed to generate FCM token';
      }
    } catch (error) {
      this.error = 'Error generating FCM token';
      console.error('Token generation failed:', error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Delete current FCM token
   */
  async deleteToken(): Promise<void> {
    this.isLoading = true;
    this.error = null;

    try {
      const success = await this.fcmService.deleteToken();
      if (!success) {
        this.error = 'Failed to delete FCM token';
      }
    } catch (error) {
      this.error = 'Error deleting FCM token';
      console.error('Token deletion failed:', error);
    } finally {
      this.isLoading = false;
    }
  }

  /**
   * Copy token to clipboard
   */
  async copyToken(): Promise<void> {
    if (!this.currentToken) return;

    try {
      await navigator.clipboard.writeText(this.currentToken);
      // You could show a toast notification here
      console.log('Token copied to clipboard');
    } catch (error) {
      console.error('Failed to copy token:', error);
    }
  }

  /**
   * Clear message history
   */
  clearMessages(): void {
    this.recentMessages = [];
  }

  /**
   * Clear event log
   */
  clearEvents(): void {
    this.recentEvents = [];
  }

  /**
   * Get permission status display text
   */
  getPermissionStatusText(): string {
    if (!this.permissionStatus) return 'Unknown';
    
    switch (this.permissionStatus.permission) {
      case 'granted': return 'Granted';
      case 'denied': return 'Denied';
      case 'default': return 'Not requested';
      default: return 'Unknown';
    }
  }

  /**
   * Get permission status CSS class
   */
  getPermissionStatusClass(): string {
    if (!this.permissionStatus) return 'status-unknown';
    
    switch (this.permissionStatus.permission) {
      case 'granted': return 'status-granted';
      case 'denied': return 'status-denied';
      case 'default': return 'status-default';
      default: return 'status-unknown';
    }
  }

  /**
   * Get FCM status display text
   */
  getFCMStatusText(): string {
    if (!this.fcmStatus) return 'Initializing...';
    
    if (!this.fcmStatus.isInitialized) return 'Not initialized';
    if (this.fcmStatus.error) return `Error: ${this.fcmStatus.error}`;
    if (!this.fcmStatus.hasPermission) return 'Permission required';
    if (!this.fcmStatus.token) return 'No token';
    
    return 'Ready';
  }

  /**
   * Get FCM status CSS class
   */
  getFCMStatusClass(): string {
    if (!this.fcmStatus) return 'status-loading';
    
    if (this.fcmStatus.error) return 'status-error';
    if (!this.fcmStatus.isInitialized || !this.fcmStatus.hasPermission) return 'status-warning';
    if (this.fcmStatus.token) return 'status-success';
    
    return 'status-default';
  }

  /**
   * Format timestamp for display
   */
  formatTimestamp(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(new Date(date));
  }

  /**
   * Get event type display text
   */
  getEventTypeText(type: FCMEventType): string {
    switch (type) {
      case FCMEventType.TOKEN_RECEIVED: return 'Token Received';
      case FCMEventType.TOKEN_REFRESHED: return 'Token Refreshed';
      case FCMEventType.MESSAGE_RECEIVED: return 'Message Received';
      case FCMEventType.PERMISSION_GRANTED: return 'Permission Granted';
      case FCMEventType.PERMISSION_DENIED: return 'Permission Denied';
      case FCMEventType.ERROR_OCCURRED: return 'Error Occurred';
      default: return type;
    }
  }
}
