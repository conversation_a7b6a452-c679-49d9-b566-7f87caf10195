import { Component, inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { FCMService } from '../../services/fcm.service';

@Component({
  selector: 'app-test-notification',
  standalone: true,
  imports: [CommonModule, FormsModule],
  template: `
    <div class="test-notification">
      <h3>🧪 Test Notifications</h3>
      <p>Send test notifications to verify FCM is working correctly.</p>
      
      <div class="test-form">
        <div class="form-group">
          <label for="title">Notification Title:</label>
          <input 
            id="title"
            type="text" 
            [(ngModel)]="testNotification.title" 
            placeholder="Enter notification title"
            class="form-input">
        </div>
        
        <div class="form-group">
          <label for="body">Notification Body:</label>
          <textarea 
            id="body"
            [(ngModel)]="testNotification.body" 
            placeholder="Enter notification message"
            class="form-input"
            rows="3"></textarea>
        </div>
        
        <div class="form-group">
          <label for="icon">Icon URL (optional):</label>
          <input 
            id="icon"
            type="url" 
            [(ngModel)]="testNotification.icon" 
            placeholder="https://example.com/icon.png"
            class="form-input">
        </div>
        
        <div class="form-group">
          <label for="clickAction">Click Action URL (optional):</label>
          <input 
            id="clickAction"
            type="url" 
            [(ngModel)]="testNotification.click_action" 
            placeholder="https://example.com/page"
            class="form-input">
        </div>
        
        <button 
          (click)="sendTestNotification()" 
          [disabled]="!canSendNotification()"
          class="btn btn-primary">
          Send Test Notification
        </button>
      </div>
      
      @if (lastTestResult) {
        <div class="test-result" [class]="lastTestResult.success ? 'success' : 'error'">
          <strong>{{ lastTestResult.success ? '✅ Success' : '❌ Error' }}</strong>
          <p>{{ lastTestResult.message }}</p>
          @if (lastTestResult.timestamp) {
            <small>{{ formatTimestamp(lastTestResult.timestamp) }}</small>
          }
        </div>
      }
      
      <div class="test-info">
        <h4>📋 Testing Instructions</h4>
        <ol>
          <li>Ensure notification permission is granted</li>
          <li>Generate an FCM token</li>
          <li>Fill in the notification details above</li>
          <li>Click "Send Test Notification"</li>
          <li>The notification should appear immediately (foreground test)</li>
        </ol>
        
        <h4>🌐 External Testing</h4>
        <p>To test background notifications:</p>
        <ol>
          <li>Copy your FCM token from the Token Management section</li>
          <li>Go to <a href="https://console.firebase.google.com" target="_blank">Firebase Console</a></li>
          <li>Navigate to Cloud Messaging</li>
          <li>Click "Send your first message"</li>
          <li>Enter title and message</li>
          <li>Click "Send test message"</li>
          <li>Paste your token and click "Test"</li>
          <li>Minimize or switch away from this tab to test background notifications</li>
        </ol>
      </div>
    </div>
  `,
  styles: [`
    .test-notification {
      background: white;
      border: 1px solid #e8eaed;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    }
    
    .test-notification h3 {
      margin: 0 0 1rem 0;
      color: #1a73e8;
      font-size: 1.2rem;
    }
    
    .test-form {
      margin: 1.5rem 0;
    }
    
    .form-group {
      margin-bottom: 1rem;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 0.5rem;
      font-weight: 500;
      color: #202124;
    }
    
    .form-input {
      width: 100%;
      padding: 0.75rem;
      border: 1px solid #dadce0;
      border-radius: 6px;
      font-size: 0.9rem;
      transition: border-color 0.2s ease;
    }
    
    .form-input:focus {
      outline: none;
      border-color: #1a73e8;
      box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.1);
    }
    
    .btn {
      padding: 0.75rem 1.5rem;
      border: none;
      border-radius: 6px;
      font-size: 0.9rem;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
    }
    
    .btn:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
    
    .btn-primary {
      background: #1a73e8;
      color: white;
    }
    
    .btn-primary:hover:not(:disabled) {
      background: #1557b0;
    }
    
    .test-result {
      margin: 1rem 0;
      padding: 1rem;
      border-radius: 8px;
      border: 1px solid;
    }
    
    .test-result.success {
      background: #e8f5e8;
      border-color: #137333;
      color: #137333;
    }
    
    .test-result.error {
      background: #fce8e6;
      border-color: #d93025;
      color: #d93025;
    }
    
    .test-result p {
      margin: 0.5rem 0;
    }
    
    .test-result small {
      font-size: 0.8rem;
      opacity: 0.8;
    }
    
    .test-info {
      margin-top: 2rem;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;
    }
    
    .test-info h4 {
      margin: 0 0 1rem 0;
      color: #202124;
      font-size: 1rem;
    }
    
    .test-info ol {
      margin: 0;
      padding-left: 1.5rem;
    }
    
    .test-info li {
      margin-bottom: 0.5rem;
      line-height: 1.4;
    }
    
    .test-info a {
      color: #1a73e8;
      text-decoration: none;
    }
    
    .test-info a:hover {
      text-decoration: underline;
    }
  `]
})
export class TestNotificationComponent {
  private readonly fcmService = inject(FCMService);
  
  testNotification = {
    title: 'Test Notification',
    body: 'This is a test notification from your Angular FCM demo app!',
    icon: '/favicon.ico',
    click_action: window.location.origin
  };
  
  lastTestResult: {
    success: boolean;
    message: string;
    timestamp: Date;
  } | null = null;
  
  /**
   * Check if we can send a test notification
   */
  canSendNotification(): boolean {
    return !!(
      this.testNotification.title &&
      this.testNotification.body &&
      Notification.permission === 'granted'
    );
  }
  
  /**
   * Send a test notification
   */
  sendTestNotification(): void {
    if (!this.canSendNotification()) {
      this.lastTestResult = {
        success: false,
        message: 'Cannot send notification. Check that permission is granted and required fields are filled.',
        timestamp: new Date()
      };
      return;
    }
    
    try {
      // Create a test notification using the browser's Notification API
      const notification = new Notification(this.testNotification.title, {
        body: this.testNotification.body,
        icon: this.testNotification.icon || '/favicon.ico',
        tag: 'test-notification',
        requireInteraction: false
      });
      
      notification.onclick = () => {
        window.focus();
        notification.close();
        
        if (this.testNotification.click_action) {
          window.location.href = this.testNotification.click_action;
        }
      };
      
      // Auto close after 5 seconds
      setTimeout(() => notification.close(), 5000);
      
      this.lastTestResult = {
        success: true,
        message: 'Test notification sent successfully! You should see it appear now.',
        timestamp: new Date()
      };
      
    } catch (error) {
      console.error('Error sending test notification:', error);
      this.lastTestResult = {
        success: false,
        message: `Failed to send test notification: ${error}`,
        timestamp: new Date()
      };
    }
  }
  
  /**
   * Format timestamp for display
   */
  formatTimestamp(date: Date): string {
    return new Intl.DateTimeFormat('en-US', {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    }).format(date);
  }
}
