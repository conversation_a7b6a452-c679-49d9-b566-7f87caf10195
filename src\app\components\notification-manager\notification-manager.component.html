<div class="notification-manager">
  <div class="header">
    <h2>🔔 Firebase Cloud Messaging</h2>
    <p>Manage push notifications for this application</p>
  </div>

  <!-- Loading Indicator -->
  @if (isLoading) {
    <div class="loading">
      <div class="spinner"></div>
      <span>Processing...</span>
    </div>
  }

  <!-- Error Display -->
  @if (error) {
    <div class="error-banner">
      <span class="error-icon">⚠️</span>
      <span>{{ error }}</span>
      <button (click)="error = null" class="close-btn">×</button>
    </div>
  }

  <!-- Status Cards -->
  <div class="status-grid">
    <!-- Permission Status -->
    <div class="status-card">
      <h3>Permission Status</h3>
      <div class="status-indicator">
        <span [class]="getPermissionStatusClass()">
          {{ getPermissionStatusText() }}
        </span>
      </div>
      @if (permissionStatus?.isSupported) {
        <p class="status-detail">Notifications are supported</p>
      } @else {
        <p class="status-detail error">Notifications not supported</p>
      }
      
      @if (permissionStatus?.canRequest && permissionStatus?.permission !== 'granted') {
        <button 
          (click)="requestPermission()" 
          [disabled]="isLoading"
          class="btn btn-primary">
          Request Permission
        </button>
      }
    </div>

    <!-- FCM Status -->
    <div class="status-card">
      <h3>FCM Status</h3>
      <div class="status-indicator">
        <span [class]="getFCMStatusClass()">
          {{ getFCMStatusText() }}
        </span>
      </div>
      @if (fcmStatus?.lastUpdated) {
        <p class="status-detail">
          Last updated: {{ formatTimestamp(fcmStatus!.lastUpdated) }}
        </p>
      }
    </div>
  </div>

  <!-- Token Management -->
  <div class="section">
    <div class="section-header">
      <h3>FCM Token Management</h3>
      <button 
        (click)="showTokenDetails = !showTokenDetails"
        class="btn btn-secondary">
        {{ showTokenDetails ? 'Hide' : 'Show' }} Details
      </button>
    </div>

    <div class="token-actions">
      <button 
        (click)="generateToken()" 
        [disabled]="isLoading || !fcmStatus?.hasPermission"
        class="btn btn-primary">
        Generate Token
      </button>
      
      @if (currentToken) {
        <button 
          (click)="copyToken()" 
          class="btn btn-secondary">
          Copy Token
        </button>
        
        <button 
          (click)="deleteToken()" 
          [disabled]="isLoading"
          class="btn btn-danger">
          Delete Token
        </button>
      }
    </div>

    @if (showTokenDetails && currentToken) {
      <div class="token-details">
        <h4>Current Token:</h4>
        <div class="token-display">
          <code>{{ currentToken }}</code>
        </div>
        <p class="token-info">
          Use this token to send push notifications to this device.
        </p>
      </div>
    }
  </div>

  <!-- Message History -->
  <div class="section">
    <div class="section-header">
      <h3>Recent Messages ({{ recentMessages.length }})</h3>
      <div class="section-actions">
        <button 
          (click)="showMessageHistory = !showMessageHistory"
          class="btn btn-secondary">
          {{ showMessageHistory ? 'Hide' : 'Show' }}
        </button>
        @if (recentMessages.length > 0) {
          <button 
            (click)="clearMessages()"
            class="btn btn-outline">
            Clear
          </button>
        }
      </div>
    </div>

    @if (showMessageHistory) {
      @if (recentMessages.length === 0) {
        <div class="empty-state">
          <p>No messages received yet</p>
          <small>Messages will appear here when notifications are received</small>
        </div>
      } @else {
        <div class="message-list">
          @for (message of recentMessages; track message.messageId) {
            <div class="message-item">
              <div class="message-header">
                <strong>{{ message.notification?.title || 'No Title' }}</strong>
                @if (message.messageId) {
                  <span class="message-id">{{ message.messageId }}</span>
                }
              </div>
              @if (message.notification?.body) {
                <p class="message-body">{{ message.notification!.body }}</p>
              }
              @if (message.data && Object.keys(message.data).length > 0) {
                <div class="message-data">
                  <strong>Data:</strong>
                  <pre>{{ message.data | json }}</pre>
                </div>
              }
            </div>
          }
        </div>
      }
    }
  </div>

  <!-- Event Log -->
  <div class="section">
    <div class="section-header">
      <h3>Event Log ({{ recentEvents.length }})</h3>
      <div class="section-actions">
        <button 
          (click)="showEventLog = !showEventLog"
          class="btn btn-secondary">
          {{ showEventLog ? 'Hide' : 'Show' }}
        </button>
        @if (recentEvents.length > 0) {
          <button 
            (click)="clearEvents()"
            class="btn btn-outline">
            Clear
          </button>
        }
      </div>
    </div>

    @if (showEventLog) {
      @if (recentEvents.length === 0) {
        <div class="empty-state">
          <p>No events logged yet</p>
          <small>FCM events will appear here</small>
        </div>
      } @else {
        <div class="event-list">
          @for (event of recentEvents; track $index) {
            <div class="event-item">
              <div class="event-header">
                <span class="event-type">{{ getEventTypeText(event.type) }}</span>
                <span class="event-time">{{ formatTimestamp(event.timestamp) }}</span>
              </div>
              @if (event.data) {
                <div class="event-data">
                  <pre>{{ event.data | json }}</pre>
                </div>
              }
            </div>
          }
        </div>
      }
    }
  </div>

  <!-- Test Notifications -->
  <app-test-notification></app-test-notification>

  <!-- Instructions -->
  <div class="section instructions">
    <h3>📋 Setup Instructions</h3>
    <ol>
      <li>Install Firebase: <code>npm install firebase</code></li>
      <li>Configure your Firebase project credentials in <code>src/environments/environment.ts</code></li>
      <li>Add your VAPID key to the environment configuration</li>
      <li>Update the service worker with your Firebase config</li>
      <li>Request notification permission and generate a token</li>
      <li>Use the token to send test notifications from Firebase Console</li>
    </ol>
  </div>
</div>
