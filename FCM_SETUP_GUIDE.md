# Firebase Cloud Messaging (FCM) Setup Guide

This guide will help you set up Firebase Cloud Messaging in your Angular 19 application.

## Prerequisites

- Node.js and npm installed
- Angular CLI 19.x
- A Firebase project (create one at [Firebase Console](https://console.firebase.google.com))

## Step 1: Install Firebase SDK

```bash
npm install firebase
```

## Step 2: Firebase Project Setup

1. Go to [Firebase Console](https://console.firebase.google.com)
2. Create a new project or select an existing one
3. Navigate to Project Settings (gear icon)
4. Go to the "General" tab
5. Scroll down to "Your apps" section
6. Click "Add app" and select the web platform (</>) 
7. Register your app with a nickname
8. Copy the Firebase configuration object

## Step 3: Configure Environment Variables

Update `src/environments/environment.ts` and `src/environments/environment.prod.ts`:

```typescript
export const environment = {
  production: false, // true for prod
  firebase: {
    apiKey: 'your-api-key',
    authDomain: 'your-project.firebaseapp.com',
    projectId: 'your-project-id',
    storageBucket: 'your-project.appspot.com',
    messagingSenderId: 'your-sender-id',
    appId: 'your-app-id',
    vapidKey: 'your-vapid-key' // See step 4
  }
};
```

## Step 4: Generate VAPID Key

1. In Firebase Console, go to Project Settings
2. Navigate to the "Cloud Messaging" tab
3. Scroll down to "Web configuration"
4. Click "Generate key pair" under "Web push certificates"
5. Copy the generated key and add it to your environment as `vapidKey`

## Step 5: Update Service Worker Configuration

Update `public/firebase-messaging-sw.js` with your Firebase configuration:

```javascript
const firebaseConfig = {
  apiKey: 'your-api-key',
  authDomain: 'your-project.firebaseapp.com',
  projectId: 'your-project-id',
  storageBucket: 'your-project.appspot.com',
  messagingSenderId: 'your-sender-id',
  appId: 'your-app-id'
};
```

## Step 6: Enable Cloud Messaging

1. In Firebase Console, go to "Cloud Messaging"
2. If prompted, enable the Cloud Messaging API
3. You're now ready to send and receive messages

## Step 7: Test the Implementation

1. Start your development server:
   ```bash
   ng serve
   ```

2. Open your browser and navigate to `http://localhost:4200`

3. Click "Request Permission" to allow notifications

4. Click "Generate Token" to get your FCM registration token

5. Copy the token and test sending a notification from Firebase Console:
   - Go to Firebase Console > Cloud Messaging
   - Click "Send your first message"
   - Enter a title and message
   - Click "Send test message"
   - Paste your token and click "Test"

## Step 8: Production Deployment

For production deployment:

1. Update `src/environments/environment.prod.ts` with production Firebase config
2. Build your app: `ng build --configuration production`
3. Deploy the built files to your hosting service
4. Ensure the service worker is accessible at `/firebase-messaging-sw.js`

## Features Implemented

✅ **Permission Management**
- Request notification permissions
- Check permission status
- Handle permission denial gracefully

✅ **Token Management**
- Generate FCM registration tokens
- Handle token refresh automatically
- Store tokens locally for persistence
- Delete tokens when needed

✅ **Message Handling**
- Receive foreground notifications
- Handle background notifications via service worker
- Display custom notification UI
- Handle notification clicks

✅ **Error Handling**
- Comprehensive error handling for all FCM operations
- User-friendly error messages
- Logging for debugging

✅ **UI Components**
- Complete notification management interface
- Real-time status updates
- Message history and event logging
- Token display and management

## Troubleshooting

### Common Issues

1. **"Firebase SDK not loaded" error**
   - Ensure Firebase is installed: `npm install firebase`
   - Check that Firebase scripts are loaded in `index.html`

2. **"Messaging not supported" error**
   - FCM only works on HTTPS (except localhost)
   - Ensure you're testing on a supported browser

3. **Permission denied**
   - Users must manually grant permission
   - Permission can't be requested again if denied
   - Users must manually enable in browser settings

4. **Service worker not registering**
   - Check that `firebase-messaging-sw.js` is accessible at root
   - Verify Angular build configuration includes the service worker

5. **Token not generating**
   - Ensure VAPID key is correctly configured
   - Check that notification permission is granted
   - Verify Firebase project configuration

### Browser Support

FCM is supported in:
- Chrome 50+
- Firefox 44+
- Safari 16.4+ (with limitations)
- Edge 17+

### Security Considerations

- Never expose Firebase config in client-side code for production
- Use Firebase Security Rules to protect your data
- Validate all incoming messages on your server
- Implement proper authentication for sensitive notifications

## Next Steps

1. **Server Integration**: Set up a backend service to send notifications
2. **Topic Subscriptions**: Implement topic-based messaging
3. **Analytics**: Track notification engagement
4. **Advanced Features**: Add rich notifications with images and actions
5. **Testing**: Implement comprehensive testing for FCM functionality

## Resources

- [Firebase Cloud Messaging Documentation](https://firebase.google.com/docs/cloud-messaging)
- [Web Push Notifications Guide](https://web.dev/push-notifications-overview/)
- [Service Worker API](https://developer.mozilla.org/en-US/docs/Web/API/Service_Worker_API)
- [Angular Firebase](https://github.com/angular/angularfire)

## Support

If you encounter issues:
1. Check the browser console for error messages
2. Verify your Firebase configuration
3. Test with a simple notification from Firebase Console
4. Check the FCM event log in the application UI
