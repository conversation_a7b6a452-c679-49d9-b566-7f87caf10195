import { environment } from '../../environments/environment';

/**
 * Firebase configuration for the application
 */
export const firebaseConfig = environment.firebase;

/**
 * Firebase Cloud Messaging configuration
 */
export const fcmConfig = {
  vapidKey: environment.firebase.vapidKey,
  serviceWorkerPath: '/firebase-messaging-sw.js',
  
  // Default notification options
  defaultNotificationOptions: {
    icon: '/favicon.ico',
    badge: '/favicon.ico',
    requireInteraction: false,
    silent: false,
    renotify: true,
    tag: 'fcm-notification'
  },
  
  // Notification actions
  notificationActions: [
    {
      action: 'open',
      title: 'Open App',
      icon: '/favicon.ico'
    },
    {
      action: 'dismiss',
      title: 'Dismiss',
      icon: '/favicon.ico'
    }
  ]
};

/**
 * FCM topic names for subscription management
 */
export const fcmTopics = {
  ALL_USERS: 'all-users',
  ANNOUNCEMENTS: 'announcements',
  UPDATES: 'updates',
  PROMOTIONS: 'promotions'
};

/**
 * FCM message types
 */
export enum FCMMessageType {
  NOTIFICATION = 'notification',
  DATA = 'data',
  BOTH = 'both'
}

/**
 * FCM priority levels
 */
export enum FCMPriority {
  NORMAL = 'normal',
  HIGH = 'high'
}
