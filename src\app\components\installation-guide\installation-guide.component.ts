import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
  selector: 'app-installation-guide',
  standalone: true,
  imports: [CommonModule],
  template: `
    <div class="installation-guide">
      <h3>🚀 Quick Setup Guide</h3>
      
      <div class="step">
        <h4>Step 1: Install Firebase SDK</h4>
        <div class="code-block">
          <code>npm install firebase</code>
        </div>
        <p>Run this command in your terminal to install the Firebase SDK.</p>
      </div>
      
      <div class="step">
        <h4>Step 2: Create Firebase Project</h4>
        <ol>
          <li>Go to <a href="https://console.firebase.google.com" target="_blank">Firebase Console</a></li>
          <li>Create a new project or select existing one</li>
          <li>Enable Cloud Messaging in the project settings</li>
          <li>Generate a VAPID key for web push</li>
        </ol>
      </div>
      
      <div class="step">
        <h4>Step 3: Configure Environment</h4>
        <p>Update <code>src/environments/environment.ts</code> with your Firebase config:</p>
        <div class="code-block">
          <pre>{{ sampleConfig }}</pre>
        </div>
      </div>
      
      <div class="step">
        <h4>Step 4: Update Service Worker</h4>
        <p>Update <code>public/firebase-messaging-sw.js</code> with your Firebase config (same as above, but without the vapidKey).</p>
      </div>
      
      <div class="step">
        <h4>Step 5: Test the Setup</h4>
        <ol>
          <li>Restart your development server: <code>ng serve</code></li>
          <li>Request notification permission</li>
          <li>Generate FCM token</li>
          <li>Send test notification from Firebase Console</li>
        </ol>
      </div>
      
      <div class="troubleshooting">
        <h4>🔧 Troubleshooting</h4>
        <ul>
          <li><strong>HTTPS Required:</strong> FCM only works on HTTPS (except localhost)</li>
          <li><strong>Browser Support:</strong> Use Chrome, Firefox, or Edge for best results</li>
          <li><strong>Permission Denied:</strong> Users must manually enable notifications in browser settings if previously denied</li>
          <li><strong>Service Worker:</strong> Ensure the service worker is accessible at the root path</li>
        </ul>
      </div>
      
      <div class="links">
        <h4>📚 Helpful Links</h4>
        <ul>
          <li><a href="https://console.firebase.google.com" target="_blank">Firebase Console</a></li>
          <li><a href="https://firebase.google.com/docs/cloud-messaging/js/client" target="_blank">FCM Web Documentation</a></li>
          <li><a href="https://web.dev/push-notifications-overview/" target="_blank">Web Push Guide</a></li>
        </ul>
      </div>
    </div>
  `,
  styles: [`
    .installation-guide {
      background: #f8f9ff;
      border: 1px solid #e8eaff;
      border-radius: 12px;
      padding: 1.5rem;
      margin-bottom: 1.5rem;
    }
    
    .installation-guide h3 {
      margin: 0 0 1.5rem 0;
      color: #1a73e8;
      font-size: 1.2rem;
    }
    
    .step {
      margin-bottom: 2rem;
      padding: 1rem;
      background: white;
      border-radius: 8px;
      border: 1px solid #e8eaed;
    }
    
    .step h4 {
      margin: 0 0 1rem 0;
      color: #202124;
      font-size: 1rem;
    }
    
    .code-block {
      background: #f1f3f4;
      border: 1px solid #dadce0;
      border-radius: 6px;
      padding: 1rem;
      margin: 1rem 0;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
      overflow-x: auto;
    }
    
    .code-block code {
      background: none;
      padding: 0;
    }
    
    .step ol, .step ul {
      margin: 0.5rem 0;
      padding-left: 1.5rem;
    }
    
    .step li {
      margin-bottom: 0.5rem;
      line-height: 1.4;
    }
    
    .troubleshooting {
      background: #fef7f0;
      border: 1px solid #f9ab00;
      border-radius: 8px;
      padding: 1rem;
      margin: 1rem 0;
    }
    
    .troubleshooting h4 {
      margin: 0 0 1rem 0;
      color: #b06000;
    }
    
    .troubleshooting ul {
      margin: 0;
      padding-left: 1.5rem;
    }
    
    .troubleshooting li {
      margin-bottom: 0.75rem;
      line-height: 1.4;
    }
    
    .links {
      background: #e8f5e8;
      border: 1px solid #137333;
      border-radius: 8px;
      padding: 1rem;
      margin: 1rem 0;
    }
    
    .links h4 {
      margin: 0 0 1rem 0;
      color: #137333;
    }
    
    .links ul {
      margin: 0;
      padding-left: 1.5rem;
    }
    
    .links li {
      margin-bottom: 0.5rem;
    }
    
    a {
      color: #1a73e8;
      text-decoration: none;
    }
    
    a:hover {
      text-decoration: underline;
    }
    
    code {
      background: #f1f3f4;
      padding: 0.25rem 0.5rem;
      border-radius: 4px;
      font-family: 'Courier New', monospace;
      font-size: 0.9rem;
    }
    
    pre {
      margin: 0;
      white-space: pre-wrap;
      word-break: break-word;
    }
  `]
})
export class InstallationGuideComponent {
  sampleConfig = `export const environment = {
  production: false,
  firebase: {
    apiKey: 'your-api-key-here',
    authDomain: 'your-project.firebaseapp.com',
    projectId: 'your-project-id',
    storageBucket: 'your-project.appspot.com',
    messagingSenderId: 'your-sender-id',
    appId: 'your-app-id',
    vapidKey: 'your-vapid-key-here'
  }
};`;
}
