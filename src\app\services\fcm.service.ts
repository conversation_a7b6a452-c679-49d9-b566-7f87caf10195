import { Injectable, inject } from '@angular/core';
import { BehaviorSubject, Observable, Subject, from, of } from 'rxjs';
import { catchError, map, switchMap, tap } from 'rxjs/operators';
import { environment } from '../../environments/environment';
import {
  FCMMessage,
  FCMTokenInfo,
  NotificationPermissionStatus,
  FCMServiceStatus,
  FCMError,
  FCMEvent,
  FCMEventType
} from '../interfaces/fcm.interface';

// Firebase imports (will be available after npm install firebase)
declare const firebase: any;

@Injectable({
  providedIn: 'root'
})
export class FCMService {
  private messaging: any;
  private isInitialized = false;
  private tokenCheckInterval: any;

  // Observables for reactive state management
  private tokenSubject = new BehaviorSubject<string | null>(null);
  private statusSubject = new BehaviorSubject<FCMServiceStatus>({
    isInitialized: false,
    hasPermission: false,
    token: null,
    error: null,
    lastUpdated: new Date()
  });
  private eventsSubject = new Subject<FCMEvent>();
  private messagesSubject = new Subject<FCMMessage>();

  // Public observables
  public readonly token$ = this.tokenSubject.asObservable();
  public readonly status$ = this.statusSubject.asObservable();
  public readonly events$ = this.eventsSubject.asObservable();
  public readonly messages$ = this.messagesSubject.asObservable();

  constructor() {
    this.initializeFirebase();
  }

  /**
   * Initialize Firebase and FCM
   */
  private async initializeFirebase(): Promise<void> {
    try {
      // Check if Firebase is available
      if (typeof firebase === 'undefined') {
        const errorMsg = 'Firebase SDK not loaded. Please install Firebase: npm install firebase';
        console.error(errorMsg);
        this.handleError('FIREBASE_NOT_LOADED', errorMsg);
        return;
      }

      // Check if environment config is available
      if (!environment.firebase || !environment.firebase.apiKey) {
        const errorMsg = 'Firebase configuration not found. Please update src/environments/environment.ts';
        console.error(errorMsg);
        this.handleError('FIREBASE_CONFIG_MISSING', errorMsg);
        return;
      }

      // Initialize Firebase app if not already initialized
      if (!firebase.apps.length) {
        firebase.initializeApp(environment.firebase);
        console.log('Firebase app initialized');
      }

      // Check if messaging is supported
      if (!firebase.messaging.isSupported()) {
        const errorMsg = 'Firebase Messaging is not supported in this browser. Please use HTTPS or a supported browser.';
        console.error(errorMsg);
        this.handleError('MESSAGING_NOT_SUPPORTED', errorMsg);
        return;
      }

      // Initialize messaging
      this.messaging = firebase.messaging();
      this.isInitialized = true;

      // Set up message handlers
      this.setupMessageHandlers();

      this.updateStatus({ isInitialized: true });
      this.emitEvent(FCMEventType.TOKEN_RECEIVED, { initialized: true });
      console.log('FCM service initialized successfully');

    } catch (error) {
      console.error('Failed to initialize Firebase:', error);
      this.handleError('INIT_ERROR', 'Failed to initialize Firebase', error);
    }
  }

  /**
   * Request notification permission from user
   */
  public async requestPermission(): Promise<NotificationPermissionStatus> {
    try {
      if (!this.isInitialized) {
        throw new Error('FCM not initialized');
      }

      const permission = await Notification.requestPermission();
      const status: NotificationPermissionStatus = {
        permission,
        isSupported: 'Notification' in window,
        canRequest: permission !== 'denied'
      };

      if (permission === 'granted') {
        this.updateStatus({ hasPermission: true });
        this.emitEvent(FCMEventType.PERMISSION_GRANTED, status);

        // Get token after permission is granted
        await this.getToken();
      } else {
        this.emitEvent(FCMEventType.PERMISSION_DENIED, status);
      }

      return status;
    } catch (error) {
      console.error('Error requesting permission:', error);
      this.handleError('PERMISSION_ERROR', 'Failed to request permission', error);
      throw error;
    }
  }

  /**
   * Get FCM registration token
   */
  public async getToken(vapidKey?: string): Promise<string | null> {
    try {
      if (!this.isInitialized) {
        throw new Error('FCM not initialized');
      }

      if (Notification.permission !== 'granted') {
        console.warn('Notification permission not granted');
        return null;
      }

      const token = await this.messaging.getToken({
        vapidKey: vapidKey || this.getVapidKey()
      });

      if (token) {
        this.tokenSubject.next(token);
        this.updateStatus({ token });
        this.emitEvent(FCMEventType.TOKEN_RECEIVED, { token });
        console.log('FCM token received:', token);

        // Store token in localStorage for persistence
        this.storeToken(token);
      }

      return token;
    } catch (error) {
      console.error('Error getting FCM token:', error);
      this.handleError('TOKEN_ERROR', 'Failed to get FCM token', error);
      return null;
    }
  }

  /**
   * Delete FCM token
   */
  public async deleteToken(): Promise<boolean> {
    try {
      if (!this.isInitialized) {
        throw new Error('FCM not initialized');
      }

      await this.messaging.deleteToken();
      this.tokenSubject.next(null);
      this.updateStatus({ token: null });
      this.removeStoredToken();

      return true;
    } catch (error) {
      console.error('Error deleting FCM token:', error);
      this.handleError('DELETE_TOKEN_ERROR', 'Failed to delete FCM token', error);
      return false;
    }
  }

  /**
   * Check current notification permission status
   */
  public getPermissionStatus(): NotificationPermissionStatus {
    return {
      permission: Notification.permission,
      isSupported: 'Notification' in window,
      canRequest: Notification.permission !== 'denied'
    };
  }

  /**
   * Get stored token from localStorage
   */
  public getStoredToken(): FCMTokenInfo | null {
    try {
      const stored = localStorage.getItem('fcm_token_info');
      return stored ? JSON.parse(stored) : null;
    } catch (error) {
      console.error('Error getting stored token:', error);
      return null;
    }
  }

  /**
   * Setup message handlers for foreground messages
   */
  private setupMessageHandlers(): void {
    if (!this.messaging) return;

    // Handle foreground messages
    this.messaging.onMessage((payload: FCMMessage) => {
      console.log('Foreground message received:', payload);
      this.messagesSubject.next(payload);
      this.emitEvent(FCMEventType.MESSAGE_RECEIVED, payload);

      // Show notification if app is in foreground
      this.showForegroundNotification(payload);
    });

    // Note: onTokenRefresh is deprecated in Firebase v9+
    // Token refresh is now handled automatically by the SDK
    // Set up periodic token checking as an alternative
    this.startTokenMonitoring();
    console.log('Message handlers set up successfully');
  }

  /**
   * Start monitoring token changes (alternative to deprecated onTokenRefresh)
   */
  private startTokenMonitoring(): void {
    // Check for token changes every 30 minutes
    this.tokenCheckInterval = setInterval(async () => {
      try {
        const currentToken = this.tokenSubject.value;
        const newToken = await this.getToken();

        if (newToken && newToken !== currentToken) {
          console.log('Token refreshed:', newToken);
          this.emitEvent(FCMEventType.TOKEN_REFRESHED, {
            oldToken: currentToken,
            newToken
          });
        }
      } catch (error) {
        console.error('Error checking token:', error);
      }
    }, 30 * 60 * 1000); // 30 minutes
  }

  /**
   * Show notification when app is in foreground
   */
  private showForegroundNotification(payload: FCMMessage): void {
    if (!payload.notification) return;

    const { title, body, icon } = payload.notification;

    if ('Notification' in window && Notification.permission === 'granted') {
      const notification = new Notification(title || 'New Message', {
        body: body || '',
        icon: icon || '/favicon.ico',
        tag: payload.messageId || 'default',
        data: payload.data,
        requireInteraction: false
      });

      notification.onclick = () => {
        window.focus();
        notification.close();

        // Handle click action if provided
        if (payload.notification?.click_action) {
          window.location.href = payload.notification.click_action;
        }
      };

      // Auto close after 5 seconds
      setTimeout(() => notification.close(), 5000);
    }
  }

  /**
   * Store token in localStorage
   */
  private storeToken(token: string): void {
    try {
      const tokenInfo: FCMTokenInfo = {
        token,
        timestamp: Date.now(),
        isValid: true
      };
      localStorage.setItem('fcm_token_info', JSON.stringify(tokenInfo));
      console.log('Token stored:', tokenInfo);
    } catch (error) {
      console.error('Error storing token:', error);
    }
  }

  /**
   * Remove stored token from localStorage
   */
  private removeStoredToken(): void {
    try {
      localStorage.removeItem('fcm_token_info');
    } catch (error) {
      console.error('Error removing stored token:', error);
    }
  }

  /**
   * Get VAPID key from environment
   */
  private getVapidKey(): string {
    return environment.firebase.vapidKey;
  }

  /**
   * Update service status
   */
  private updateStatus(updates: Partial<FCMServiceStatus>): void {
    const currentStatus = this.statusSubject.value;
    const newStatus: FCMServiceStatus = {
      ...currentStatus,
      ...updates,
      lastUpdated: new Date()
    };
    this.statusSubject.next(newStatus);
  }

  /**
   * Emit FCM event
   */
  private emitEvent(type: FCMEventType, data?: any): void {
    const event: FCMEvent = {
      type,
      data,
      timestamp: new Date()
    };
    this.eventsSubject.next(event);
  }

  /**
   * Handle errors
   */
  private handleError(code: string, message: string, details?: any): void {
    const error: FCMError = { code, message, details };
    this.updateStatus({ error: message });
    this.emitEvent(FCMEventType.ERROR_OCCURRED, error);
  }

  /**
   * Cleanup resources
   */
  public destroy(): void {
    // Clear token monitoring interval
    if (this.tokenCheckInterval) {
      clearInterval(this.tokenCheckInterval);
      this.tokenCheckInterval = null;
    }

    // Complete observables
    this.tokenSubject.complete();
    this.statusSubject.complete();
    this.eventsSubject.complete();
    this.messagesSubject.complete();
  }
}
